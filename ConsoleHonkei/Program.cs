using System;
using System.Threading;
using System.Windows.Forms;

namespace ConsoleHonkei
{
    class Program
    {
        private static MouseHook? mouseHook;
        private static bool isRunning = true;

        static void Main(string[] args)
        {
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Middle Mouse Desktop Clicker started");
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Press 'q' and Enter to quit");
            Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Listening for middle mouse clicks...");

            try
            {
                // Initialize the mouse hook
                mouseHook = new MouseHook();
                mouseHook.InstallHook();

                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Mouse hook installed successfully");

                // Start a background thread to check for quit command
                Thread inputThread = new Thread(CheckForQuitCommand)
                {
                    IsBackground = true
                };
                inputThread.Start();

                // Keep the application running with a message pump
                // Use Application.Run() with a message-only window for better hook processing
                Application.Run(new MessageOnlyForm());
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Error: {ex.Message}");
            }
            finally
            {
                // Clean up
                mouseHook?.UninstallHook();
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Application terminated");
            }
        }

        private static void CheckForQuitCommand()
        {
            while (isRunning)
            {
                string? input = Console.ReadLine();
                if (input?.ToLower() == "q")
                {
                    isRunning = false;
                    mouseHook?.UninstallHook();
                    Application.Exit();
                    break;
                }
            }
        }
    }
}