using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Threading;
using System.Windows.Forms;

namespace ConsoleHonkei
{
    public class MouseHook
    {
        private const int WH_MOUSE_LL = 14;
        private const int WM_MBUTTONDOWN = 0x0207;
        private const uint MOUSEEVENTF_LEFTDOWN = 0x0002;
        private const uint MOUSEEVENTF_LEFTUP = 0x0004;
        private const int INPUT_MOUSE = 0;

        private static LowLevelMouseProc _proc = HookCallback;
        private static IntPtr _hookID = IntPtr.Zero;

        public delegate IntPtr LowLevelMouseProc(int nCode, IntPtr wParam, IntPtr lParam);

        [StructLayout(LayoutKind.Sequential)]
        public struct POINT
        {
            public int x;
            public int y;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MSLLHOOKSTRUCT
        {
            public POINT pt;
            public uint mouseData;
            public uint flags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct INPUT
        {
            public int type;
            public MouseInput mi;
        }

        [StructLayout(LayoutKind.Sequential)]
        public struct MouseInput
        {
            public int dx;
            public int dy;
            public uint mouseData;
            public uint dwFlags;
            public uint time;
            public IntPtr dwExtraInfo;
        }

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern IntPtr SetWindowsHookEx(int idHook,
            LowLevelMouseProc lpfn, IntPtr hMod, uint dwThreadId);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        [return: MarshalAs(UnmanagedType.Bool)]
        public static extern bool UnhookWindowsHookEx(IntPtr hhk);

        [DllImport("user32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern IntPtr CallNextHookEx(IntPtr hhk, int nCode,
            IntPtr wParam, IntPtr lParam);

        [DllImport("kernel32.dll", CharSet = CharSet.Auto, SetLastError = true)]
        public static extern IntPtr GetModuleHandle(string lpModuleName);

        [DllImport("user32.dll", SetLastError = true)]
        public static extern uint SendInput(uint nInputs, INPUT[] pInputs, int cbSize);

        [DllImport("user32.dll")]
        public static extern int GetSystemMetrics(int nIndex);

        public void InstallHook()
        {
            _hookID = SetHook(_proc);
        }

        public void UninstallHook()
        {
            if (_hookID != IntPtr.Zero)
            {
                UnhookWindowsHookEx(_hookID);
                _hookID = IntPtr.Zero;
            }
        }

        private static IntPtr SetHook(LowLevelMouseProc proc)
        {
            using (Process curProcess = Process.GetCurrentProcess())
            using (ProcessModule? curModule = curProcess.MainModule)
            {
                if (curModule?.ModuleName != null)
                {
                    return SetWindowsHookEx(WH_MOUSE_LL, proc,
                        GetModuleHandle(curModule.ModuleName), 0);
                }
            }
            return IntPtr.Zero;
        }

        public static IntPtr HookCallback(int nCode, IntPtr wParam, IntPtr lParam)
        {
            if (nCode >= 0 && wParam == (IntPtr)WM_MBUTTONDOWN)
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Middle mouse button clicked detected!");
                
                // Execute two desktop clicks with delay
                PerformDesktopClicks();
            }

            return CallNextHookEx(_hookID, nCode, wParam, lParam);
        }

        private static void PerformDesktopClicks()
        {
            try
            {
                // Get desktop center coordinates
                int screenWidth = GetSystemMetrics(0);  // SM_CXSCREEN
                int screenHeight = GetSystemMetrics(1); // SM_CYSCREEN

                // First click
                SimulateMouseClick(1628, 2742);
                
                // Small delay between clicks
                Thread.Sleep(200);
                
                // Second click
                SimulateMouseClick(1143, 2257);
                
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Desktop clicks completed");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[{DateTime.Now:HH:mm:ss}] Error performing desktop clicks: {ex.Message}");
            }
        }

        private static void SimulateMouseClick(int x, int y)
        {
            // Convert screen coordinates to normalized coordinates (0-65535)
            int normalizedX = (x * 65535) / GetSystemMetrics(0);
            int normalizedY = (y * 65535) / GetSystemMetrics(1);

            INPUT[] inputs = new INPUT[2];

            // Mouse down
            inputs[0] = new INPUT
            {
                type = INPUT_MOUSE,
                mi = new MouseInput
                {
                    dx = normalizedX, 
                    dy = normalizedY,
                    mouseData = 0,
                    dwFlags = MOUSEEVENTF_LEFTDOWN | 0x8000, // MOUSEEVENTF_ABSOLUTE
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            };

            // Mouse up
            inputs[1] = new INPUT
            {
                type = INPUT_MOUSE,
                mi = new MouseInput
                {
                    dx = normalizedX,
                    dy = normalizedY,
                    mouseData = 0,
                    dwFlags = MOUSEEVENTF_LEFTUP | 0x8000, // MOUSEEVENTF_ABSOLUTE
                    time = 0,
                    dwExtraInfo = IntPtr.Zero
                }
            };

            SendInput(2, inputs, Marshal.SizeOf(typeof(INPUT)));
        }
    }
}
